import swaggerJSD<PERSON> from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "IonAlumni API",
      version: "1.0.0",
      description: "Alumni Portal API Documentation",
      contact: {
        name: "API Support",
        email: "<EMAIL>",
      },
      license: {
        name: "ISC",
        url: "https://opensource.org/licenses/ISC",
      },
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 5000}`,
        description: "Development server",
      },
      {
        url: "https://api.ionalumni.com",
        description: "Production server",
      },
    ],
    tags: [
      {
        name: "Authentication",
        description: "User authentication and authorization endpoints",
      },
      {
        name: "Users",
        description: "User management endpoints",
      },
      {
        name: "System",
        description: "System health and monitoring endpoints",
      },
      {
        name: "Development",
        description: "Development and debugging endpoints",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter JWT token",
        },
        cookieAuth: {
          type: "apiKey",
          in: "cookie",
          name: "refreshToken",
          description: "Refresh token stored in HTTP-only cookie",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            id: {
              type: "string",
              description: "User unique identifier",
            },
            email: {
              type: "string",
              format: "email",
              description: "User email address",
            },
            firstName: {
              type: "string",
              description: "User first name",
            },
            lastName: {
              type: "string",
              description: "User last name",
            },
            graduationYear: {
              type: "integer",
              description: "Year of graduation",
            },
            department: {
              type: "string",
              description: "Department/Course",
            },
            currentCompany: {
              type: "string",
              description: "Current company",
            },
            currentPosition: {
              type: "string",
              description: "Current job position",
            },
            location: {
              type: "string",
              description: "Current location",
            },
            bio: {
              type: "string",
              description: "User biography",
            },
            profilePicture: {
              type: "string",
              description: "Profile picture URL",
            },
            isApproved: {
              type: "boolean",
              description: "Whether user is approved by admin",
            },
            createdAt: {
              type: "string",
              format: "date-time",
              description: "Account creation timestamp",
            },
            updatedAt: {
              type: "string",
              format: "date-time",
              description: "Last update timestamp",
            },
          },
        },
        AuthResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
            },
            message: {
              type: "string",
            },
            data: {
              type: "object",
              properties: {
                user: {
                  $ref: "#/components/schemas/User",
                },
                accessToken: {
                  type: "string",
                  description: "JWT access token",
                },
              },
            },
          },
        },
        Error: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              description: "Error message",
            },
            errors: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  field: {
                    type: "string",
                  },
                  message: {
                    type: "string",
                  },
                },
              },
            },
          },
        },
        PaginatedResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
            },
            data: {
              type: "array",
              items: {},
            },
            pagination: {
              type: "object",
              properties: {
                page: {
                  type: "integer",
                },
                limit: {
                  type: "integer",
                },
                total: {
                  type: "integer",
                },
                totalPages: {
                  type: "integer",
                },
              },
            },
          },
        },
        LoginRequest: {
          type: "object",
          required: ["email", "password", "tenant_id"],
          properties: {
            email: {
              type: "string",
              format: "email",
              example: "<EMAIL>",
            },
            password: {
              type: "string",
              example: "password123",
            },
            tenant_id: {
              type: "integer",
              minimum: 1,
              example: 2,
              description: "Valid tenant ID (required)",
            },
          },
        },
        RegisterRequest: {
          type: "object",
          required: ["email", "password", "full_name", "tenant_id", "role"],
          properties: {
            email: {
              type: "string",
              format: "email",
              example: "<EMAIL>",
              description: "Valid email address",
            },
            password: {
              type: "string",
              minLength: 8,
              example: "MyPassword123!",
              description:
                "Password must be at least 8 characters with uppercase, lowercase, number, and special character",
            },
            full_name: {
              type: "string",
              minLength: 2,
              maxLength: 100,
              example: "John Doe",
              description: "Full name (2-100 characters)",
            },
            mobile_number: {
              type: "string",
              example: "+1234567890",
              description: "Valid mobile phone number (optional)",
            },
            tenant_id: {
              type: "integer",
              minimum: 1,
              example: 2,
              description: "Valid tenant ID (required)",
            },
            role: {
              type: "string",
              enum: ["STUDENT", "ALUMNUS", "TENANT_ADMIN", "SUPER_ADMIN"],
              example: "STUDENT",
              description: "User role",
            },
            usn: {
              type: "string",
              pattern: "^[A-Z]{2,4}\\d{7}$",
              example: "CS2021001",
              description:
                "University Seat Number: 2-4 uppercase letters + 7 digits (required for STUDENT and ALUMNUS roles)",
            },
            course_name: {
              type: "string",
              minLength: 2,
              maxLength: 100,
              example: "Computer Science",
              description: "Course name (required for STUDENT and ALUMNUS roles)",
            },
            batch_year: {
              type: "integer",
              minimum: 1900,
              maximum: 2035,
              example: 2020,
              description: "Batch year (required for STUDENT and ALUMNUS roles)",
            },
          },
        },
        SuccessResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Operation completed successfully",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2025-07-24T12:00:00.000Z",
            },
          },
        },
        ForgotPasswordRequest: {
          type: "object",
          required: ["email", "tenant_id"],
          properties: {
            email: {
              type: "string",
              format: "email",
              example: "<EMAIL>",
              description: "Email address for password reset",
            },
            tenant_id: {
              type: "integer",
              minimum: 1,
              example: 2,
              description: "Valid tenant ID (required)",
            },
          },
        },
        ResetPasswordRequest: {
          type: "object",
          required: ["password", "tenant_id"],
          properties: {
            password: {
              type: "string",
              minLength: 8,
              example: "NewPassword123!",
              description: "New password (must meet complexity requirements)",
            },
            tenant_id: {
              type: "integer",
              minimum: 1,
              example: 2,
              description: "Valid tenant ID (required)",
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./src/routes/*.ts", "./src/controllers/*.ts"],
};

export const swaggerSpec = swaggerJSDoc(options);

export const swaggerUiOptions = {
  explorer: true,
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #3b82f6 }
  `,
  customSiteTitle: "IonAlumni API Documentation",
  swaggerOptions: {
    persistAuthorization: true, // Keep auth tokens between page refreshes
    displayRequestDuration: true,
    filter: true,
    tryItOutEnabled: true,
    defaultModelsExpandDepth: 1, // Show schema details by default
  },
};

export { swaggerUi };
